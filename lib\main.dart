/// good working

// import 'dart:io';
// import 'package:animated_splash_screen/animated_splash_screen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get_navigation/get_navigation.dart';
// import 'package:get/get_navigation/src/root/get_material_app.dart';
// import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
// import 'api/MyHttpOverrides.dart';
// import 'dashboard/Dashboard.dart';
// import 'sip_account/SIPConfiguration.dart';
//
// /*  LETEST DESIGN
//
//   Activity name : Main activity
//   Project name : iSalesCRM Mobile App
//
// */
// //Launch Screen
//
// /// new
// final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
// final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//     FlutterLocalNotificationsPlugin();
//
// void main() async {
//   navigatorKey;
//   WidgetsFlutterBinding.ensureInitialized();
//
//   const AndroidInitializationSettings initializationSettingsAndroid =
//       AndroidInitializationSettings('@mipmap/ic_launcher');
//
//   final InitializationSettings initializationSettings = InitializationSettings(
//     android: initializationSettingsAndroid,
//   );
//
//   await flutterLocalNotificationsPlugin.initialize(
//     initializationSettings,
//     onDidReceiveNotificationResponse: (NotificationResponse response) {
//       if (response.payload != null) {
//         final List<String> payloadData = response.payload!.split('|');
//         if (payloadData.length == 2) {
//           String phoneNumber = payloadData[0];
//           String callerName = payloadData[1];
//           // Ensure handling only on user action
//           if (response.actionId == 'answer') {
//             SIPConfiguration.handleNotificationAction(
//               'answer',
//               navigatorKey.currentContext!,
//               phoneNumber,
//               callerName,
//             );
//           } else if (response.actionId == 'reject') {
//             SIPConfiguration.handleNotificationAction(
//               'reject',
//               navigatorKey.currentContext!,
//               phoneNumber,
//               callerName,
//             );
//           }
//         }
//       }
//     },
//   );
//
//   HttpOverrides.global = MyHttpOverrides();
//   runApp(const MyApp());
// }
//
// class MyApp extends StatelessWidget {
//   const MyApp({super.key});
//
//   // This widget is the root of your application.
//   @override
//   Widget build(BuildContext context){
//     return MaterialApp(
//       navigatorKey: navigatorKey,
//       debugShowCheckedModeBanner: false,
//       //theme: ThemeData.light(),
//       home: SplashScreen(),
//     );
//   }
// }
//
// // SplashScreen
// class SplashScreen extends StatefulWidget {
//   const SplashScreen({super.key});
//
//   @override
//   State<SplashScreen> createState() => _SplashScreenState();
// }
//
// class _SplashScreenState extends State<SplashScreen> {
//   bool loginStatus = false;
//   InternetConnectionCheckerPlus internetConnectionCheckerPlus =
//       InternetConnectionCheckerPlus();
//
//   @override
//   initState() {
//     // TODO: implement initState
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return ScreenUtilInit(
//       child: GetMaterialApp(
//         debugShowCheckedModeBanner: false,
//         home: animatedIconShow(),
//       ),
//     );
//   }
//
// //Show animated splash screen then redirect to User First Screen
//   animatedIconShow() {
//     return AnimatedSplashScreen(
//         splash: Image.asset("assets/images/logo.png"),
//         splashTransition: SplashTransition.scaleTransition,
//         duration: 3000,
//         nextScreen: const DashboardScreen());
//   }
// }

///

import 'dart:async';
import 'dart:io';

import 'package:dialercall/sip_account/SIPDialPad.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'api/MyHttpOverrides.dart';
import 'services/background_call_service.dart';

import 'sip_account/SIPConfiguration.dart';

/*  LETEST DESIGN

  Activity name : Main activity
  Project name : iSalesCRM Mobile App

*/
//Launch Screen

/// new
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

void main() async {
  // Add global error handling
  runZonedGuarded(() async {
    try {
      navigatorKey;
      WidgetsFlutterBinding.ensureInitialized();

      print("🚀 [MAIN] App initialization started");

      // Create notification channel for Android
      await _createNotificationChannel();

      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      final InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
      );

      print("📱 [MAIN] Initializing notification plugin");
      await flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse response) {
          try {
            print(
                "🔔 [MAIN] Notification response received: ${response.actionId}");
            print("🔔 [MAIN] Notification payload: ${response.payload}");

            if (response.payload != null) {
              final List<String> payloadData = response.payload!.split('|');
              if (payloadData.length == 2) {
                String phoneNumber = payloadData[0];
                String callerName = payloadData[1];
                print(
                    "📞 [MAIN] Processing notification action: ${response.actionId} for $callerName ($phoneNumber)");

                // Ensure handling only on user action
                if (response.actionId == 'answer') {
                  print("✅ [MAIN] User chose to answer call");
                  if (navigatorKey.currentContext != null) {
                    SIPConfiguration.handleNotificationAction(
                      'answer',
                      navigatorKey.currentContext!,
                      phoneNumber,
                      callerName,
                    );
                  }
                } else if (response.actionId == 'reject') {
                  print("❌ [MAIN] User chose to reject call");
                  if (navigatorKey.currentContext != null) {
                    SIPConfiguration.handleNotificationAction(
                      'reject',
                      navigatorKey.currentContext!,
                      phoneNumber,
                      callerName,
                    );
                  }
                }
              } else {
                print("⚠️ [MAIN] Invalid payload format: ${response.payload}");
              }
            } else {
              print("⚠️ [MAIN] Notification response has no payload");
            }
          } catch (e, stackTrace) {
            print("❌ [MAIN] Error handling notification response: $e");
            print("📍 [MAIN] Stack trace: $stackTrace");
          }
        },
      );

      print("🌐 [MAIN] Setting up HTTP overrides");
      HttpOverrides.global = MyHttpOverrides();

      // Initialize background service for call handling - but don't start it yet
      print("🔧 [MAIN] Initializing background service");
      try {
        await BackgroundCallService.initializeService();
        print("✅ [MAIN] Background service initialized");
        // Note: Service will be started after permissions are granted
      } catch (e, stackTrace) {
        print("⚠️ [MAIN] Background service initialization failed: $e");
        print("📍 [MAIN] Stack trace: $stackTrace");
        // Continue without background service to prevent app crash
      }

      print("🚀 [MAIN] Starting app");
      runApp(const MyApp());
    } catch (e, stackTrace) {
      print("❌ [MAIN] Critical error during app initialization: $e");
      print("📍 [MAIN] Stack trace: $stackTrace");
      // Still try to run the app with minimal functionality
      runApp(const MyApp());
    }
  }, (error, stackTrace) {
    print("❌ [MAIN] Uncaught error: $error");
    print("📍 [MAIN] Stack trace: $stackTrace");
  });
}

Future<void> _createNotificationChannel() async {
  print("📢 [MAIN] Creating notification channel");

  if (Platform.isAndroid) {
    final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
        flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    if (androidImplementation != null) {
      // Create call notification channel
      const AndroidNotificationChannel callChannel = AndroidNotificationChannel(
        'call_channel_id',
        'Call Notifications',
        description: 'Notifications for incoming calls',
        importance: Importance.max,
        playSound: true,
        enableVibration: true,
        enableLights: true,
        ledColor: Color.fromARGB(255, 255, 0, 0),
        showBadge: true,
      );

      await androidImplementation.createNotificationChannel(callChannel);
      print("✅ [MAIN] Call notification channel created successfully");

      // Request notification permissions for Android 13+
      final bool? granted =
          await androidImplementation.requestNotificationsPermission();
      print("🔐 [MAIN] Notification permission granted: $granted");
    } else {
      print("❌ [MAIN] Failed to get Android notification implementation");
    }
  }
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_, child) {
        return MaterialApp(
          navigatorKey: navigatorKey,
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            primarySwatch: Colors.blue,
            visualDensity: VisualDensity.adaptivePlatformDensity,
          ),
          home: const SplashScreen(),
        );
      },
    );
  }
}

/// new splash screen good work
///
class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<double> _loadingTextOpacity;
  String _loadingText = "Getting everything ready...";
  void checkSipAccountStatus() async {
    try {
      var ref = await SharedPreferences.getInstance();

      String? sipID = ref.getString("sipID");
      String? sipDomain = ref.getString("sipDomain");
      String? sipPassword = ref.getString("sipPassword");

      if (sipID != null &&
          sipDomain != null &&
          sipPassword != null &&
          mounted) {
        try {
          SIPConfiguration.config(sipID, sipDomain, sipPassword, true, context);
        } catch (e) {
          print("Error configuring SIP: $e");
          // Continue without SIP configuration to prevent app crash
        }
      }
    } catch (e) {
      print("Error checking SIP account status: $e");
      // Continue without SIP configuration to prevent app crash
    }
  }

  @override
  void initState() {
    super.initState();
    // Delay SIP account check to ensure app is fully initialized
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        checkSipAccountStatus();
      }
    });
    // Initialize animations
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutBack,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeIn,
      ),
    );

    _loadingTextOpacity = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.8, 1.0),
      ),
    );

    // Start animation and initialize
    _controller.forward();
    _initialize();

    // Setup loading text animation
    _animateLoadingText();
  }

  void _animateLoadingText() {
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _loadingText = "Please wait...";
        });
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            setState(() {
              _loadingText = "Getting everything ready...";
            });
            _animateLoadingText();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _initialize() async {
    try {
      await _requestPermissions();
    } catch (e) {
      print("⚠️ [PERMISSIONS] Permission request failed: $e");
      // Continue with app initialization even if permissions fail
    }

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        try {
          Navigator.pushReplacement(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  const SipDialPad(
                phoneNumber: "",
                callerName: "",
              ),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
              transitionDuration: const Duration(seconds: 1),
            ),
          );
        } catch (e) {
          print("❌ [NAVIGATION] Failed to navigate: $e");
          // Try a simpler navigation as fallback
          try {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => const SipDialPad(
                  phoneNumber: "",
                  callerName: "",
                ),
              ),
            );
          } catch (e2) {
            print("❌ [NAVIGATION] Fallback navigation also failed: $e2");
            // App will stay on splash screen if all navigation fails
          }
        }
      }
    });
  }

  Future<void> _requestPermissions() async {
    print("🔐 [PERMISSIONS] Requesting permissions");

    // Request each permission individually with error handling
    await _requestSinglePermission(Permission.notification, "Notification");
    await _requestSinglePermission(Permission.microphone, "Microphone");
    await _requestSinglePermission(Permission.phone, "Phone");
    await _requestSinglePermission(Permission.contacts, "Contacts");
    await _requestSinglePermission(Permission.storage, "Storage");
    await _requestSinglePermission(
        Permission.systemAlertWindow, "System Alert Window");
    await _requestSinglePermission(
        Permission.ignoreBatteryOptimizations, "Battery Optimization");

    print("✅ [PERMISSIONS] All permissions requested");

    // Now start the background service after permissions are handled
    await _startBackgroundServiceSafely();
  }

  Future<void> _startBackgroundServiceSafely() async {
    try {
      print("▶️ [PERMISSIONS] Starting background service after permissions");
      await BackgroundCallService.startService();
      print("✅ [PERMISSIONS] Background service started successfully");
    } catch (e, stackTrace) {
      print("⚠️ [PERMISSIONS] Failed to start background service: $e");
      print("📍 [PERMISSIONS] Stack trace: $stackTrace");
      // Continue without background service to prevent app crash
    }
  }

  Future<void> _requestSinglePermission(
      Permission permission, String name) async {
    try {
      final status = await permission.request();
      print("🔐 [PERMISSIONS] $name permission: $status");
    } catch (e) {
      print("⚠️ [PERMISSIONS] Failed to request $name permission: $e");
      // Continue with other permissions even if one fails
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue[400]!,
              Colors.blue[800]!,
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // Background design elements
              Positioned(
                top: screenHeight * 0.1,
                right: -screenWidth * 0.2,
                child: Transform.rotate(
                  angle: -0.2,
                  child: Container(
                    width: screenWidth * 0.7,
                    height: screenWidth * 0.7,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30),
                      color: Colors.white.withOpacity(0.1),
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: -screenHeight * 0.1,
                left: -screenWidth * 0.3,
                child: Container(
                  width: screenWidth * 0.8,
                  height: screenWidth * 0.8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
              ),

              // Main content
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated logo
                    AnimatedBuilder(
                      animation: _controller,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value,
                          child: Opacity(
                            opacity: _opacityAnimation.value,
                            child: Container(
                              padding: EdgeInsets.all(screenWidth * 0.05),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 20,
                                    offset: const Offset(0, 10),
                                  ),
                                ],
                              ),
                              child: Image.asset(
                                "assets/images/logo.png",
                                width: screenWidth * 0.35,
                                height: screenWidth * 0.35,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    SizedBox(height: screenHeight * 0.06),

                    // Animated loading text
                    AnimatedOpacity(
                      opacity: 1.0,
                      duration: const Duration(seconds: 1),
                      child: Text(
                        _loadingText,
                        style: TextStyle(
                          fontSize: screenWidth * 0.045,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 1.2,
                        ),
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.04),

                    // Enhanced loading indicator with pulse animation
                    TweenAnimationBuilder(
                      tween: Tween<double>(begin: 0.8, end: 1.2),
                      duration: const Duration(milliseconds: 1000),
                      builder: (context, double value, child) {
                        return Transform.scale(
                          scale: value,
                          child: SizedBox(
                            width: screenWidth * 0.12,
                            height: screenWidth * 0.12,
                            child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                              strokeWidth: 4,
                              backgroundColor: Colors.white.withOpacity(0.2),
                            ),
                          ),
                        );
                      },
                      onEnd: () => setState(() {}), // Restart the animation
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
