import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_background_service_android/flutter_background_service_android.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:voip24h_sdk_mobile/callkit/utils/sip_event.dart';
import 'package:voip24h_sdk_mobile/voip24h_sdk_mobile.dart';
import '../sip_account/SIPConfiguration.dart';
import '../database/DBHandler.dart';

class BackgroundCallService {
  static const String _serviceId = 'call_background_service';

  static Future<void> initializeService() async {
    try {
      print("🔧 [BACKGROUND_SERVICE] Initializing background service");

      final service = FlutterBackgroundService();

      // Configure the notification for the background service
      const AndroidNotificationChannel channel = AndroidNotificationChannel(
        'background_service_channel',
        'Background Call Service',
        description: 'This channel is used for background call handling',
        importance: Importance.low,
      );

      final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
          FlutterLocalNotificationsPlugin();

      if (Platform.isAndroid) {
        try {
          await flutterLocalNotificationsPlugin
              .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin>()
              ?.createNotificationChannel(channel);
          print("✅ [BACKGROUND_SERVICE] Notification channel created");
        } catch (e) {
          print(
              "⚠️ [BACKGROUND_SERVICE] Failed to create notification channel: $e");
        }
      }

      await service.configure(
        androidConfiguration: AndroidConfiguration(
          onStart: onStart,
          autoStart: true,
          isForegroundMode: true,
          notificationChannelId: 'background_service_channel',
          initialNotificationTitle: 'Call Service',
          initialNotificationContent: 'Ready to receive calls',
          foregroundServiceNotificationId: 888,
        ),
        iosConfiguration: IosConfiguration(
          autoStart: true,
          onForeground: onStart,
          onBackground: onIosBackground,
        ),
      );

      print("✅ [BACKGROUND_SERVICE] Background service configured");
    } catch (e, stackTrace) {
      print("❌ [BACKGROUND_SERVICE] Failed to initialize service: $e");
      print("📍 [BACKGROUND_SERVICE] Stack trace: $stackTrace");
      // Don't rethrow to prevent app crash
    }
  }

  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) async {
    print("🚀 [BACKGROUND_SERVICE] Service started");

    DartPluginRegistrant.ensureInitialized();

    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    // Initialize notification plugin for background service
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
    );
    await flutterLocalNotificationsPlugin.initialize(initializationSettings);

    if (service is AndroidServiceInstance) {
      service.on('setAsForeground').listen((event) {
        service.setAsForegroundService();
      });

      service.on('setAsBackground').listen((event) {
        service.setAsBackgroundService();
      });
    }

    service.on('stopService').listen((event) {
      service.stopSelf();
    });

    // Set up SIP event listener in background service
    _setupSipEventListener(flutterLocalNotificationsPlugin);

    // Keep the service alive and monitor for incoming calls
    Timer.periodic(const Duration(seconds: 30), (timer) async {
      if (service is AndroidServiceInstance) {
        if (await service.isForegroundService()) {
          // Update notification to show service is active
          flutterLocalNotificationsPlugin.show(
            888,
            'Call Service Active',
            'Monitoring for incoming calls...',
            const NotificationDetails(
              android: AndroidNotificationDetails(
                'background_service_channel',
                'Background Call Service',
                icon: 'ic_bg_service_small',
                ongoing: true,
              ),
            ),
          );
        }
      }

      // Check if we need to stop the service
      final prefs = await SharedPreferences.getInstance();
      final shouldStop = prefs.getBool('stop_background_service') ?? false;
      if (shouldStop) {
        timer.cancel();
        service.stopSelf();
      }
    });
  }

  static void _setupSipEventListener(
      FlutterLocalNotificationsPlugin notificationPlugin) {
    print("🔧 [BACKGROUND_SERVICE] Setting up SIP event listener");

    try {
      // Listen to SIP events in background service
      Voip24hSdkMobile.callModule.eventStreamController.stream.listen(
          (event) async {
        print("📞 [BACKGROUND_SERVICE] SIP event received: ${event['event']}");

        switch (event['event']) {
          case SipEvent.Ring:
            String? callerName = "Unknown";
            var body = event['body'];
            print("📞 [BACKGROUND_SERVICE] Ring event: $body");

            if (body['callType'] == "inbound") {
              String phoneNumber = body['phoneNumber'].toString();
              callerName = body['callerName'] ?? "Unknown";

              print(
                  "📞 [BACKGROUND_SERVICE] Incoming call from: $phoneNumber ($callerName)");

              // Store incoming call in database
              await _storeIncomingCallInBackground(phoneNumber, callerName!);

              // Show notification from background service
              await _showCallNotificationFromBackground(
                  notificationPlugin, callerName!, phoneNumber);
            }
            break;

          case SipEvent.Hangup:
          case SipEvent.Error:
            print(
                "📞 [BACKGROUND_SERVICE] Call ended or error: ${event['body']}");
            // Dismiss call notification
            await notificationPlugin.cancel(0);
            break;
        }
      }, onError: (error) {
        print("❌ [BACKGROUND_SERVICE] SIP event stream error: $error");
      });

      print("✅ [BACKGROUND_SERVICE] SIP event listener set up successfully");
    } catch (e) {
      print("❌ [BACKGROUND_SERVICE] Failed to set up SIP event listener: $e");
    }
  }

  @pragma('vm:entry-point')
  static Future<bool> onIosBackground(ServiceInstance service) async {
    print("📱 [BACKGROUND_SERVICE] iOS background mode");
    WidgetsFlutterBinding.ensureInitialized();
    DartPluginRegistrant.ensureInitialized();
    return true;
  }

  static Future<void> startService() async {
    try {
      print("▶️ [BACKGROUND_SERVICE] Starting service");
      final service = FlutterBackgroundService();
      var isRunning = await service.isRunning();
      if (!isRunning) {
        service.startService();
        print("✅ [BACKGROUND_SERVICE] Service started successfully");
      } else {
        print("ℹ️ [BACKGROUND_SERVICE] Service already running");
      }
    } catch (e, stackTrace) {
      print("❌ [BACKGROUND_SERVICE] Failed to start service: $e");
      print("📍 [BACKGROUND_SERVICE] Stack trace: $stackTrace");
      // Don't rethrow to prevent app crash
    }
  }

  static Future<void> stopService() async {
    print("⏹️ [BACKGROUND_SERVICE] Stopping service");
    final service = FlutterBackgroundService();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('stop_background_service', true);
    service.invoke("stopService");
    print("✅ [BACKGROUND_SERVICE] Service stopped");
  }

  static Future<bool> isServiceRunning() async {
    final service = FlutterBackgroundService();
    return await service.isRunning();
  }

  static Future<void> _storeIncomingCallInBackground(
      String phoneNumber, String callerName) async {
    try {
      print(
          "💾 [BACKGROUND_SERVICE] Storing incoming call: $phoneNumber ($callerName)");

      DateTime today = DateTime.now();
      String date =
          "${_numberFormat(today.day)}/${_numberFormat(today.month)}/${_numberFormat(today.year)}";
      String time =
          "${_numberFormat(today.hour)}:${_numberFormat(today.minute)}";

      // Insert or update call log using the correct DBHandler method
      await DBHandler.instance.insertANewRecord({
        "phone_number": phoneNumber,
        "name": callerName,
        "type": "Incoming",
        "date": "$date $time",
        "time": time,
      });

      print("✅ [BACKGROUND_SERVICE] Incoming call stored successfully");
    } catch (e) {
      print("❌ [BACKGROUND_SERVICE] Failed to store incoming call: $e");
    }
  }

  static String _numberFormat(int number) {
    String numberStr = number.toString();
    if (number < 10) {
      numberStr = '0$numberStr';
    }
    return numberStr;
  }

  static Future<void> _showCallNotificationFromBackground(
      FlutterLocalNotificationsPlugin notificationPlugin,
      String callerName,
      String phoneNumber) async {
    try {
      print(
          "🔔 [BACKGROUND_SERVICE] Creating call notification for $callerName ($phoneNumber)");

      final AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'call_channel_id',
        'Call Notifications',
        channelDescription: 'Notifications for incoming calls',
        importance: Importance.max,
        priority: Priority.high,
        ongoing: true,
        autoCancel: false,
        fullScreenIntent: true,
        category: AndroidNotificationCategory.call,
        visibility: NotificationVisibility.public,
        showWhen: true,
        when: DateTime.now().millisecondsSinceEpoch,
        usesChronometer: false,
        timeoutAfter: 30000, // 30 seconds timeout
        playSound: true,
        enableVibration: true,
        vibrationPattern: Int64List.fromList([0, 1000, 500, 1000]),
        enableLights: true,
        ledColor: const Color.fromARGB(255, 255, 0, 0),
        ledOnMs: 1000,
        ledOffMs: 500,
        actions: <AndroidNotificationAction>[
          const AndroidNotificationAction(
            showsUserInterface: true,
            'answer',
            'Answer',
          ),
          const AndroidNotificationAction(
            showsUserInterface: true,
            'reject',
            'Reject',
          ),
        ],
      );

      final NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);

      await notificationPlugin.show(
        0,
        'Incoming Call',
        '$callerName ($phoneNumber)',
        platformChannelSpecifics,
        payload: '$phoneNumber|$callerName',
      );

      print("✅ [BACKGROUND_SERVICE] Call notification shown successfully");
    } catch (e, stackTrace) {
      print("❌ [BACKGROUND_SERVICE] Failed to show call notification: $e");
      print("📍 [BACKGROUND_SERVICE] Stack trace: $stackTrace");
    }
  }
}
