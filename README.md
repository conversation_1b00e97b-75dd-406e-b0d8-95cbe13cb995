# DialerApp - VoIP Calling Application Documentation

## Overview
DialerApp is a Flutter-based VoIP calling application that provides SIP-based voice calling functionality with comprehensive call logging and contact management. The app uses the VoIP24h SDK for SIP communication and includes features like call history, contact management, and real-time call handling.

## Project Structure

```
DialerApp/
├── lib/
│   ├── main.dart                    # App entry point and splash screen
│   ├── sip_account/                 # SIP/VoIP functionality
│   │   ├── SIPConfiguration.dart    # SIP setup and event handling
│   │   ├── SIPCredential.dart       # SIP account credentials UI
│   │   ├── SipAccountSetting.dart   # SIP account management
│   │   ├── SipAccountStatus.dart    # SIP status tracking
│   │   ├── SipDialPad.dart         # Main dialer interface
│   │   ├── CallUI.dart             # Active call interface
│   │   └── call_logs/              # Call logging system
│   │       ├── CallLogsModel.dart   # Call log data model
│   │       ├── CallLogDetails.dart  # Call history details UI
│   │       └── CallLogDetailsModel.dart # Call history data model
│   ├── database/
│   │   └── DBHandler.dart          # SQLite database operations
│   ├── contacts/                   # Contact management
│   ├── dashboard/                  # Dashboard and home screens
│   ├── components/                 # Reusable UI components
│   └── constants/                  # App constants and themes
├── voip24h_sdk_mobile/            # VoIP SDK (local package)
├── record/                        # Call recording functionality (local package)
└── android/                       # Android-specific configurations
```

## Core Architecture

### 1. Application Entry Point (`lib/main.dart`)
- **Main Function**: Initializes the app with proper permissions and HTTP overrides
- **SplashScreen**: Shows loading screen while requesting permissions
- **Navigation**: Automatically navigates to SipDialPad after initialization
- **Permissions**: Requests microphone, phone, and notification permissions

### 2. SIP/VoIP System

#### SIP Configuration (`lib/sip_account/SIPConfiguration.dart`)
**Purpose**: Manages SIP account setup and event handling

**Key Components**:
- `SIPConfiguration.config()`: Initializes SIP connection
- Event stream listener for SIP events (Ring, Connected, Hangup, etc.)
- Automatic credential storage using SharedPreferences
- Missed call handling and notification management


**SIP Events Handled**:
- `AccountRegistrationStateChanged`: Account login status
- `Ring`: Incoming/outgoing call ringing
- `Up/Connected`: Call established
- `Hangup`: Call ended
- `Error`: Call errors
- `Missed`: Missed call notifications

#### SIP Credentials (`lib/sip_account/SIPCredential.dart`)
**Purpose**: User interface for entering SIP account details

**Features**:
- SIP User ID input
- Password field with show/hide toggle
- SIP Server configuration (default: **************:25067)
- Form validation and credential saving
- Automatic navigation to dialer on successful registration

#### VoIP24h SDK Integration (`voip24h_sdk_mobile/`)
**Purpose**: Provides low-level SIP functionality

**Key Features**:
- SIP account registration and management
- Call initiation and handling
- Audio controls (mute, speaker, hold)
- DTMF tone sending
- Call transfer capabilities

**Configuration**:
```dart
var sipConfiguration = SipConfigurationBuilder(
  extension: sipID,
  domain: sipDomain, 
  password: sipPassword,
).setKeepAlive(true)
 .setPort(25067)
 .setTransport(TransportType.Udp)
 .build();
```

### 3. Call Management System

#### Call UI (`lib/sip_account/CallUI.dart`)
**Purpose**: Manages active call interface and controls

**Features**:
- Real-time call status display
- Call timer functionality
- Audio controls (mute, speaker, hold)
- DTMF keypad for in-call dialing
- Call recording controls
- Automatic call log storage

**Call Flow**:
1. Phone number validation (11-digit format)
2. SIP call initiation via `Voip24hSdkMobile.callModule.call()`
3. Event listening for call state changes
4. Call timer management during active calls
5. Call log storage on call completion

#### Dialer Interface (`lib/sip_account/SipDialPad.dart`)
**Purpose**: Main application interface with dialer and call history

**Components**:
- **Tabbed Interface**: History and Contacts tabs
- **Dialer Keypad**: Number input with call button
- **Call History**: Recent calls with details
- **Contact Integration**: Phone contacts access
- **Search Functionality**: Search through call logs and contacts

### 4. Call Logging System

#### Database Structure (`lib/database/DBHandler.dart`)
**Database**: SQLite database (`iCRM5.db`)

**Tables**:
1. **call_log**: Main call log entries
   - `phone_number` (PRIMARY KEY)
   - `name` (Contact name)
   - `type` (Call type: Incoming/Outgoing/Missed)
   - `date` (Date and time)
   - `time` (Time only)

2. **call_log_details**: Detailed call history
   - `id` (AUTO INCREMENT)
   - `type` (Call type)
   - `date` (Call date)
   - `time` (Call time)
   - `duration` (Call duration)
   - `phone_number` (Phone number)

**Key Operations**:
- `insertANewRecord()`: Add new call log entry
- `updateLastCallLogs()`: Update existing call log
- `getCallLogs()`: Retrieve call history
- `getCallHistory()`: Get detailed history for specific number
- `getDialPadSearch()`: Search functionality

#### Call Log Models
- **CallLogsModel** (`lib/sip_account/call_logs/CallLogsModel.dart`): Main call log data structure
- **CallLogDetailsModel** (`lib/sip_account/call_logs/CallLogDetailsModel.dart`): Detailed call history structure

### 5. Contact Management
**Location**: `lib/contacts/`
- Integration with device contacts
- Contact search and display
- Direct calling from contacts
- Contact details view

## Key Dependencies

### Core Dependencies
- `flutter`: UI framework
- `voip24h_sdk_mobile`: SIP/VoIP functionality (local package)
- `sqflite`: SQLite database
- `shared_preferences`: Settings storage
- `flutter_contacts`: Device contacts access
- `permission_handler`: Runtime permissions

### Communication & Notifications
- `flutter_callkit_incoming`: Call notifications
- `flutter_local_notifications`: Local notifications
- `firebase_messaging`: Push notifications

### UI & UX
- `flutter_screenutil`: Responsive design
- `marquee`: Scrolling text
- `animated_text_kit`: Text animations
- `lite_rolling_switch`: Toggle switches

## How SIP/VoIP Calling Works

### 1. SIP Registration Process
1. User enters credentials in `SIPCredential` screen
2. `SIPConfiguration.config()` creates SIP configuration
3. VoIP24h SDK initializes SIP module
4. Registration status monitored via event stream
5. Successful registration enables calling features

### 2. Making a Call
1. User enters number in `SipDialPad`
2. Number validation (11-digit format)
3. `CallUI` screen opens with call interface
4. `Voip24hSdkMobile.callModule.call()` initiates SIP call
5. Call state events monitored (Ring → Connected → Hangup)
6. Call details stored in database

### 3. Call State Management
**Event Flow**:
- `Ring`: Call is ringing
- `Up/Connected`: Call established, timer starts
- `Hangup`: Call ended, duration calculated
- `Error`: Call failed, error handling
- `Missed`: Missed call logged

### 4. Call Logging Process
1. **During Call**: Basic call info stored
2. **Call End**: Duration and final status updated
3. **Database Storage**: Both summary and detailed records created
4. **UI Update**: Call history refreshed automatically

## Configuration Details

### SIP Server Configuration
- **Default Server**: **************:25067
- **Transport**: UDP
- **Port**: 25067
- **Keep Alive**: Enabled

### Database Configuration
- **Database Name**: iCRM5.db
- **Version**: 1
- **Location**: App's document directory

### Permissions Required
- `RECORD_AUDIO`: For voice calls
- `PHONE`: For phone state access
- `CONTACTS`: For contact integration
- `NOTIFICATIONS`: For call notifications

## Development Notes

### Key Classes and Their Responsibilities
1. **SIPConfiguration**: SIP setup and event handling
2. **CallUI**: Active call management
3. **SipDialPad**: Main dialer interface
4. **DBHandler**: Database operations
5. **VoIP24h SDK**: Low-level SIP functionality

### Important Files to Understand
- `lib/main.dart`: App initialization and permissions
- `lib/sip_account/SIPConfiguration.dart`: Core SIP logic
- `lib/sip_account/CallUI.dart`: Call handling
- `lib/database/DBHandler.dart`: Data persistence
- `voip24h_sdk_mobile/`: SIP SDK implementation

### Testing the Application
1. Configure SIP credentials in settings
2. Test call initiation and handling
3. Verify call logging functionality
4. Check contact integration
5. Test various call scenarios (successful, failed, missed)

## Troubleshooting

### Common Issues
1. **SIP Registration Failed**: Check credentials and server connectivity
2. **Call Not Connecting**: Verify network and SIP server status
3. **No Call History**: Check database permissions and initialization
4. **Audio Issues**: Verify microphone permissions

### Debug Information
- SIP events are logged to console
- Database operations include error handling
- Call states are tracked in real-time

## Detailed Code Flow Documentation

### Application Startup Flow
1. **main()** function in `lib/main.dart`:
   - Sets up HTTP overrides for network requests
   - Initializes Flutter local notifications
   - Runs MyApp widget

2. **MyApp** widget:
   - Configures screen utilities for responsive design
   - Sets up MaterialApp with navigation key
   - Launches SplashScreen as home

3. **SplashScreen**:
   - Requests necessary permissions (microphone, phone, contacts)
   - Shows animated splash with fade transition
   - Automatically navigates to SipDialPad after 1 second
   - Checks for existing SIP credentials and auto-configures if found

### SIP Account Management Flow

#### Initial Setup (`lib/sip_account/SIPCredential.dart`)
1. User enters SIP credentials:
   - SIP User ID (extension number)
   - Password (with show/hide toggle functionality)
   - SIP Server (default: **************:25067)

2. Form validation ensures all fields are filled

3. On save, calls `SIPConfiguration.config()` with credentials

4. Success leads to automatic navigation to SipDialPad

#### SIP Configuration Process (`lib/sip_account/SIPConfiguration.dart`)
1. **Configuration Creation**:
   ```dart
   var sipConfiguration = SipConfigurationBuilder(
     extension: sipID,
     domain: sipDomain,
     password: sipPassword,
   ).setKeepAlive(true).setPort(25067).setTransport(TransportType.Udp).build();
   ```

2. **SDK Initialization**:
   - Calls `Voip24hSdkMobile.callModule.initSipModule(sipConfiguration)`
   - Sets up event stream listener for SIP events

3. **Event Handling**:
   - `AccountRegistrationStateChanged`: Updates SipAccountStatus
   - `Ring`: Handles incoming/outgoing call notifications
   - `Up/Connected`: Manages call connection state
   - `Hangup`: Processes call termination
   - `Missed`: Logs missed calls to database

4. **Credential Storage**:
   - Successful registration triggers `storeSipCredential()`
   - Uses SharedPreferences for persistent storage

### Call Initiation Flow (`lib/sip_account/CallUI.dart`)

#### Pre-Call Validation
1. Phone number format validation:
   - 11-digit numbers: Direct calling
   - 13-digit numbers: Strip country code (first 2 digits)
   - Invalid lengths: Show "Forbidden" status

2. Number cleaning: Remove non-numeric characters

#### Call Process
1. **SIP Call Initiation**:
   ```dart
   Voip24hSdkMobile.callModule.call("$phoneNumber");
   ```

2. **Event Monitoring**:
   - `Ring`: Update UI to show "Ringing..." status
   - `Up/Connected`: Start call timer, enable call controls
   - `Hangup/Error`: End call, store call log, navigate back

3. **Call Controls Available**:
   - Mute/Unmute microphone
   - Speaker on/off
   - Hold/Resume call
   - DTMF keypad for in-call dialing
   - Call recording (if enabled)
   - Call transfer functionality

4. **Call Timer Management**:
   - Starts when call connects (`Up` or `Connected` event)
   - Updates every second during active call
   - Stops on call termination

### Database Operations (`lib/database/DBHandler.dart`)

#### Database Schema
```sql
-- Main call log table
CREATE TABLE call_log(
  phone_number TEXT PRIMARY KEY,
  name TEXT(50),
  type TEXT(15),
  date DATETIME,
  time TEXT(15)
);

-- Detailed call history
CREATE TABLE call_log_details(
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  type TEXT(15),
  date TEXT(15),
  time TEXT(15),
  duration TEXT(10),
  phone_number TEXT(16)
);
```

#### Call Logging Process
1. **During Call** (`storeCallLog()`):
   - Stores basic contact information
   - Uses `insertANewRecord()` for new entries

2. **Call Completion** (`storeCallLogDetails()`):
   - Calculates call duration
   - Updates main call log with final status
   - Inserts detailed history record
   - Refreshes UI call history

3. **Data Retrieval**:
   - `getCallLogs()`: Recent calls for main history view
   - `getCallHistory(phoneNumber)`: Detailed history for specific contact
   - `getDialPadSearch(keyword)`: Search functionality

### UI Navigation Flow

#### Main Interface (`lib/sip_account/SipDialPad.dart`)
- **Tabbed Interface**: History and Contacts tabs
- **History Tab**: Shows recent calls with call details
- **Contacts Tab**: Device contacts integration
- **Dialer Keypad**: Number input with call button
- **Search**: Real-time search through call logs

#### Navigation Patterns
1. **SplashScreen** → **SipDialPad** (automatic)
2. **SipDialPad** → **CallUI** (on call initiation)
3. **CallUI** → **SipDialPad** (on call end)
4. **SipDialPad** → **CallLogDetails** (on history item tap)
5. **DrawerMenu** → **SipAccountSetting** → **SIPCredential**

### VoIP24h SDK Integration

#### Android Implementation (`voip24h_sdk_mobile/android/`)
- Uses Linphone core for SIP functionality
- Handles call state changes through CoreListener
- Manages audio routing and call controls
- Provides DTMF and call transfer capabilities

#### iOS Implementation (`voip24h_sdk_mobile/ios/`)
- Swift implementation using Linphone SDK
- Similar functionality to Android version
- Handles iOS-specific call management
- Integrates with iOS CallKit for native call experience

#### Key SDK Methods
- `initSipModule(sipConfiguration)`: Initialize SIP account
- `call(phoneNumber)`: Initiate outgoing call
- `answer()`: Answer incoming call
- `hangup()`: End current call
- `mute(enabled)`: Control microphone
- `speaker(enabled)`: Control speaker output

## Advanced Features

### Call Recording (`record/` package)
- Local package for call recording functionality
- Integrates with the main app for recording controls
- Stores recordings in app's document directory

### Firebase Integration
- **Firebase Messaging**: Push notifications for incoming calls
- **Firebase Auth**: User authentication (if implemented)
- **Firebase Database**: Cloud data synchronization (if implemented)

### Contact Integration (`lib/contacts/`)
- Uses `flutter_contacts` package for device contact access
- Provides contact search and selection
- Integrates with call logging for contact name resolution
- Supports direct calling from contact list

### Notification System
- **Local Notifications**: Call status updates
- **CallKit Integration**: Native iOS call interface
- **Missed Call Notifications**: Automatic missed call alerts

## Security Considerations

### SIP Security
- Uses UDP transport for SIP communication
- Credentials stored securely using SharedPreferences
- No plain text password storage in logs

### Permissions
- Runtime permission requests for sensitive features
- Graceful handling of permission denials
- Clear permission usage explanations

### Data Privacy
- Local database storage for call logs
- No automatic cloud synchronization without user consent
- Contact data handled according to platform guidelines

## Performance Optimizations

### Database Performance
- Indexed queries for fast call log retrieval
- Limited result sets to prevent memory issues
- Efficient search algorithms for contact lookup

### UI Performance
- Lazy loading for large contact lists
- Efficient list rendering with ListView.builder
- Responsive design with flutter_screenutil

### Memory Management
- Proper disposal of controllers and streams
- Event listener cleanup on widget disposal
- Efficient image and asset loading

This comprehensive documentation provides everything needed to understand, maintain, and extend the DialerApp. The modular architecture ensures easy maintenance while the detailed flow documentation helps developers understand the complete call lifecycle from initiation to logging.
